import { useState, useEffect } from 'react'
import { Link, NavLink, useLocation } from 'react-router-dom'
import logo from '../assets/images/logo.jpg'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const location = useLocation()

  // Chiudi il menu mobile quando cambia la location (quando l'utente naviga a una nuova pagina)
  useEffect(() => {
    setIsMenuOpen(false)
  }, [location])

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const navLinkClass = ({ isActive }: { isActive: boolean }) => 
    `transition-colors duration-300 font-medium ${isActive ? 'text-accent' : 'text-white hover:text-accent'}`

  return (
    <header className="bg-secondary text-white z-50 relative">
      <div className="container py-4">
        <div className="flex items-center justify-between">
        <Link to="/" className="flex items-center space-x-2">
          <img src={logo} alt="Logo Programmarti" className="h-8 w-auto rounded-full" />
          <span className="text-2xl font-bold">Programmarti</span>
        </Link>

          {/* Mobile menu button */}
          <button 
            className="p-2 md:hidden" 
            onClick={toggleMenu}
            aria-label="Menu principale"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <NavLink to="/" className={navLinkClass}>Home</NavLink>
            <NavLink to="/chi-siamo" className={navLinkClass}>Chi Siamo</NavLink>
            <NavLink to="/servizi" className={navLinkClass}>Servizi</NavLink>
            <NavLink to="/portfolio" className={navLinkClass}>Portfolio</NavLink>
            <NavLink to="/contatti" className={navLinkClass}>Contatti</NavLink>
          </nav>
        </div>

        {/* Mobile Navigation */}
        <nav className={`${isMenuOpen ? 'flex' : 'hidden'} flex-col mt-4 space-y-4 md:hidden`}>
          <NavLink to="/" className={navLinkClass}>Home</NavLink>
          <NavLink to="/chi-siamo" className={navLinkClass}>Chi Siamo</NavLink>
          <NavLink to="/servizi" className={navLinkClass}>Servizi</NavLink>
          <NavLink to="/portfolio" className={navLinkClass}>Portfolio</NavLink>
          <NavLink to="/contatti" className={navLinkClass}>Contatti</NavLink>
        </nav>
      </div>
    </header>
  )
}

export default Header 
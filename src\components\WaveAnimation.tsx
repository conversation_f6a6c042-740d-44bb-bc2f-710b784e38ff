import { CSSProperties } from 'react';

interface WaveAnimationProps {
  color?: string;
}

const WaveAnimation = ({ color = "text-bgLight" }: WaveAnimationProps) => {
  // Estrae il colore dalla classe Tailwind (assumendo che text-bgLight sia una classe Tailwind)
  // Se non è una classe Tailwind, usa direttamente il valore fornito
  const baseColor = color.startsWith('text-') ? 'currentColor' : color;

  // Stile CSS per il componente
  const styles = {
    waveContainer: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      width: '100%',
      overflow: 'hidden',
      height: '130px',
    } as CSSProperties,
    svg: {
      position: 'relative',
      width: '100%',
      height: '15vh',
      marginBottom: '-7px', // Fix per il gap in safari
      minHeight: '100px',
      maxHeight: '150px',
    } as CSSProperties,
    parallaxUse: {
      animation: 'move-forever 25s cubic-bezier(.55,.5,.45,.5) infinite',
    } as CSSProperties,
    parallaxUse1: {
      animationDelay: '-2s',
      animationDuration: '7s',
    } as CSSProperties,
    parallaxUse2: {
      animationDelay: '-3s',
      animationDuration: '10s',
    } as CSSProperties,
    parallaxUse3: {
      animationDelay: '-4s',
      animationDuration: '13s',
    } as CSSProperties,
    parallaxUse4: {
      animationDelay: '-5s',
      animationDuration: '20s',
    } as CSSProperties,
  };

  return (
    <div style={styles.waveContainer} className={color}>
      <svg 
        style={styles.svg}
        xmlns="http://www.w3.org/2000/svg" 
        xmlnsXlink="http://www.w3.org/1999/xlink"
        viewBox="0 24 150 28" 
        preserveAspectRatio="none" 
        shapeRendering="auto"
      >
        <defs>
          <path id="onda" d="M-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352Z" />
        </defs>
        <g className="parallaxonde">
          <use 
            xlinkHref="#onda" 
            x="48" 
            y="0" 
            fill={baseColor} 
            fillOpacity="0.6" 
            style={{...styles.parallaxUse, ...styles.parallaxUse1}}
          />
          <use 
            xlinkHref="#onda" 
            x="48" 
            y="3" 
            fill={baseColor} 
            fillOpacity="0.4" 
            style={{...styles.parallaxUse, ...styles.parallaxUse2}}
          />
          <use 
            xlinkHref="#onda" 
            x="48" 
            y="5" 
            fill={baseColor} 
            fillOpacity="0.1" 
            style={{...styles.parallaxUse, ...styles.parallaxUse3}}
          />
          <use 
            xlinkHref="#onda" 
            x="48" 
            y="7" 
            fill="#f9f6ef" 
            style={{...styles.parallaxUse, ...styles.parallaxUse4}}
          />
        </g>
      </svg>

      <style>
        {`
          @keyframes move-forever {
            0% {
              transform: translate3d(-90px,0,0);
            }
            100% { 
              transform: translate3d(85px,0,0);
            }
          }
          @media (max-width: 768px) {
            svg {
              height: 40px !important;
              min-height: 40px !important;
            }
          }
        `}
      </style>
    </div>
  );
};

export default WaveAnimation; 
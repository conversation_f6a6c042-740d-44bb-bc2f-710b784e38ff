import { useState } from 'react'
import { Link } from 'react-router-dom'
import WaveAnimation from '../components/WaveAnimation'

// Definizione dell'interfaccia per i progetti
interface Project {
  id: number
  title: string
  category: string
  description: string
  fullDescription: string
  images: string[]
  tags: string[]
}

const Portfolio = () => {
  // Categorie di filtro
  const categories = ['Tutti', 'Web Development', 'E-commerce', 'Digital Marketing', 'UI/UX Design']
  const [activeCategory, setActiveCategory] = useState('Tutti')
  // Stati per la modal
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)

  // Funzione per aprire la modal
  const openModal = (project: Project) => {
    setSelectedProject(project)
    setIsModalOpen(true)
    // Previeni lo scroll quando la modal è aperta
    document.body.style.overflow = 'hidden'
  }

  // Funzione per chiudere la modal
  const closeModal = () => {
    setIsModalOpen(false)
    setSelectedProject(null)
    // Ripristina lo scroll
    document.body.style.overflow = 'auto'
  }

  // Progetti demo (in un'applicazione reale questi dati sarebbero in un CMS o database)
  const projects: Project[] = [
    {
      id: 1,
      title: 'E-commerce per Brand di Moda',
      category: 'E-commerce',
      description: 'Sviluppo di un e-commerce completo per un brand di moda, con integrazione pagamenti e gestione magazzino.',
      fullDescription: 'Abbiamo sviluppato una piattaforma e-commerce completa per un brand di moda emergente. Il progetto ha incluso la progettazione dell\'interfaccia utente, lo sviluppo front-end e back-end, l\'integrazione con gateway di pagamento Stripe, e la configurazione di un sistema di gestione del magazzino. La soluzione ha permesso al brand di aumentare le vendite online del 150% nel primo trimestre dopo il lancio.',
      images: ['moda1.jpg', 'moda2.jpg', 'moda3.jpg'],
      tags: ['E-commerce', 'React', 'Node.js', 'Stripe'],
    },
    {
      id: 2,
      title: 'Sito Web Corporate',
      category: 'Web Development',
      description: 'Realizzazione di un sito web corporate per una società di consulenza, con area riservata clienti.',
      fullDescription: 'Abbiamo progettato e sviluppato un sito web corporate completo per una società di consulenza finanziaria. Il sito include un\'area riservata per i clienti dove possono accedere a documenti, rapporti e analisi personalizzate. Il design moderno e responsivo riflette l\'identità del brand e ha migliorato significativamente la presenza online dell\'azienda.',
      images: ['corporate1.jpg', 'corporate2.jpg', 'corporate3.jpg'],
      tags: ['Web Development', 'WordPress', 'SEO'],
    },
    {
      id: 3,
      title: 'App Mobile Food Delivery',
      category: 'Web Development',
      description: 'Sviluppo app mobile per servizio di consegna cibo, con gestione ordini e tracking in tempo reale.',
      fullDescription: 'Abbiamo creato un\'applicazione mobile cross-platform per un servizio di food delivery locale. L\'app permette agli utenti di sfogliare menu, effettuare ordini, pagare e tracciare le consegne in tempo reale. Sul lato ristoratore, abbiamo implementato un sistema di gestione degli ordini che ha ottimizzato i tempi di preparazione e consegna del 30%.',
      images: ['food1.jpg', 'food2.jpg', 'food3.jpg'],
      tags: ['React Native', 'Firebase', 'UX Design'],
    },
    {
      id: 4,
      title: 'Campagna Marketing Digitale',
      category: 'Digital Marketing',
      description: 'Sviluppo e gestione di una campagna di marketing digitale multicanale per un brand nel settore fitness.',
      fullDescription: 'Abbiamo pianificato e implementato una campagna di marketing digitale integrata per un brand nel settore fitness. La strategia ha incluso advertising su Facebook e Google, email marketing, content marketing e influencer partnerships. Nel corso di 6 mesi, la campagna ha generato un ROI del 320% e ha aumentato la base clienti del brand del 45%.',
      images: ['marketing1.jpg', 'marketing2.jpg', 'marketing3.jpg'],
      tags: ['Facebook Ads', 'Google Ads', 'Email Marketing'],
    },
    {
      id: 5,
      title: 'Redesign UX/UI Piattaforma SaaS',
      category: 'UI/UX Design',
      description: 'Riprogettazione dell\'interfaccia utente e dell\'esperienza utente per una piattaforma SaaS B2B.',
      fullDescription: 'Abbiamo riprogettato completamente l\'interfaccia utente e migliorato l\'esperienza utente di una piattaforma SaaS B2B per la gestione delle risorse umane. Il processo ha incluso ricerche sugli utenti, testing di usabilità e la creazione di un design system completo. Il redesign ha portato a un aumento del 60% nel tasso di conversione dei trial e una riduzione del 40% nelle richieste di supporto legate all\'usabilità.',
      images: ['ux1.jpg', 'ux2.jpg', 'ux3.jpg'],
      tags: ['UI Design', 'UX Research', 'Figma'],
    },
    {
      id: 6,
      title: 'E-commerce B2B',
      category: 'E-commerce',
      description: 'Sviluppo di una piattaforma e-commerce B2B per azienda nel settore industriale.',
      fullDescription: 'Abbiamo sviluppato una piattaforma e-commerce B2B su misura per un\'azienda leader nel settore delle forniture industriali. La soluzione include listini prezzi personalizzati per cliente, gestione di ordini complessi, integrazione con il sistema ERP esistente e un portale admin avanzato. La piattaforma ha digitalizzato l\'80% dei processi di vendita precedentemente manuali.',
      images: ['b2b1.jpg', 'b2b2.jpg', 'b2b3.jpg'],
      tags: ['E-commerce', 'B2B', 'Magento', 'CRM'],
    },
  ]

  // Filtra i progetti in base alla categoria selezionata
  const filteredProjects = activeCategory === 'Tutti' 
    ? projects 
    : projects.filter(project => project.category === activeCategory)

  return (
    <>
      {/* Hero Section */}
      <section className="relative py-20 pb-48 text-white bg-gradient-to-r from-secondary to-primary">
        <div className="container">
          <div className="text-center">
            <h1 className="mb-6 text-4xl font-bold md:text-5xl text-shadow">
              I Nostri Lavori
            </h1>
            <p className="max-w-2xl mx-auto text-lg md:text-xl text-shadow">
              Una selezione dei progetti che abbiamo realizzato per i nostri clienti.
            </p>
          </div>
        </div>
        
        {/* Wave Shape */}
        <WaveAnimation />
      </section>

      {/* Filtri */}
      <section className="py-8 bg-bgLight">
        <div className="container">
          <div className="flex flex-wrap items-center justify-center gap-3">
            {categories.map(category => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`px-4 py-2 text-sm font-medium transition-colors duration-300 rounded-full ${
                  activeCategory === category
                    ? 'bg-primary text-white'
                    : 'bg-white text-textSecondary hover:bg-gray-100'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="section">
        <div className="container">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {filteredProjects.map(project => (
              <div 
                key={project.id} 
                className="overflow-hidden transition-transform duration-300 rounded-lg shadow-md hover:-translate-y-2 cursor-pointer"
                onClick={() => openModal(project)}
              >
                <div className="w-full h-48 overflow-hidden bg-gray-200">
                  {/* Placeholder per immagine */}
                  <div className="flex items-center justify-center w-full h-full bg-gray-300">
                    <span className="text-gray-600">Immagine Progetto {project.id}</span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="mb-2 text-xl font-bold">{project.title}</h3>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {project.tags.map(tag => (
                      <span key={tag} className="px-2 py-1 text-xs text-white rounded-full bg-primary">
                        {tag}
                      </span>
                    ))}
                  </div>
                  <p className="mb-4 text-textSecondary">
                    {project.description}
                  </p>
                  <a href="#" className="inline-block font-medium text-primary hover:text-accent">
                    Vedi caso studio &rarr;
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Modal per i dettagli del progetto */}
      {isModalOpen && selectedProject && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-70">
          <div className="relative flex flex-col w-full max-w-6xl max-h-[90vh] overflow-hidden bg-white rounded-lg shadow-xl md:flex-row">
            {/* Pulsante di chiusura */}
            <button 
              onClick={closeModal}
              className="absolute z-10 p-2 text-white transition-colors bg-primary hover:bg-accent rounded-full top-4 right-4"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            
            {/* Colonna sinistra con immagini */}
            <div className="w-full md:w-1/2 h-[300px] md:h-auto overflow-y-auto bg-gray-100">
              {selectedProject.images.map((img, index) => (
                <div key={index} className="w-full h-80 bg-gray-300 flex items-center justify-center">
                  <span className="text-gray-600">Immagine {index + 1}: {img}</span>
                </div>
              ))}
            </div>
            
            {/* Colonna destra con dettagli */}
            <div className="w-full md:w-1/2 p-8 overflow-y-auto">
              <h2 className="mb-4 text-2xl font-bold text-primary">{selectedProject.title}</h2>
              
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-700">Categoria</h3>
                <p className="text-textSecondary">{selectedProject.category}</p>
              </div>
              
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-700">Tag</h3>
                <div className="flex flex-wrap gap-2 mt-2">
                  {selectedProject.tags.map(tag => (
                    <span key={tag} className="px-3 py-1 text-sm text-white rounded-full bg-primary">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-700">Descrizione</h3>
                <p className="mt-2 text-textSecondary">
                  {selectedProject.fullDescription}
                </p>
              </div>
              
              <button 
                className="px-6 py-3 mt-8 text-white transition-colors rounded bg-primary hover:bg-accent"
              >
                Richiedi un progetto simile
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Call to Action */}
      <section className="py-16 text-white bg-gradient-to-r from-primary to-secondary">
        <div className="container text-center">
          <h2 className="mb-6 text-3xl font-bold md:text-4xl">Vuoi un progetto simile?</h2>
          <p className="max-w-2xl mx-auto mb-8 text-lg">
            Contattaci per discutere del tuo progetto e scoprire come possiamo aiutarti a realizzarlo.
          </p>
          <Link to="/contatti" className="btn border-2 border-white hover:bg-white hover:text-primary">
            Iniziamo a lavorare insieme
          </Link>
        </div>
      </section>
    </>
  )
}

export default Portfolio 
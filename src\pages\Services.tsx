import { Link } from 'react-router-dom'
import WaveAnimation from '../components/WaveAnimation'

const Services = () => {
  return (
    <>
      {/* Hero Section */}
      <section className="relative py-20 pb-48 text-white bg-gradient-to-r from-secondary to-primary">
        <div className="container">
          <div className="text-center">
            <h1 className="mb-6 text-4xl font-bold md:text-5xl text-shadow">
              I <PERSON><PERSON>
            </h1>
            <p className="max-w-2xl mx-auto text-lg md:text-xl text-shadow">
              Soluzioni digitali personalizzate per aiutare il tuo business a crescere online.
            </p>
          </div>
        </div>
        
        {/* Wave Shape */}
        <WaveAnimation />
      </section>

      {/* Web Development */}
      <section id="web-development" className="section">
        <div className="container">
          <div className="grid items-center grid-cols-1 gap-12 md:grid-cols-2">
            <div>
              <h2 className="mb-6 text-3xl font-bold">Web Development</h2>
              <p className="mb-4 text-textSecondary">
                Sviluppiamo siti web professionali, responsivi e performanti che aiutano a convertire i visitatori in clienti. Utilizziamo le tecnologie più moderne per garantire velocità, sicurezza e facilità di gestione.
              </p>
              <ul className="mb-6 space-y-2 text-textSecondary">
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Siti web aziendali e landing page</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Portali e piattaforme web personalizzate</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Sviluppo frontend (HTML, CSS, JavaScript, React)</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Sviluppo backend e API (Node.js, PHP)</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Ottimizzazione SEO e prestazioni</span>
                </li>
              </ul>
              <Link to="/contatti" className="btn btn-primary">
                Richiedi un preventivo
              </Link>
            </div>
            <div className="w-full h-64 overflow-hidden rounded-lg md:h-96 bg-gray-200">
              {/* Placeholder per immagine */}
              <div className="flex items-center justify-center w-full h-full bg-gray-300">
                <span className="text-gray-600">Immagine Web Development</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* E-commerce */}
      <section id="ecommerce" className="py-16 bg-bgLight">
        <div className="container">
          <div className="grid items-center grid-cols-1 gap-12 md:grid-cols-2">
            <div className="order-2 md:order-1 w-full h-64 overflow-hidden rounded-lg md:h-96 bg-gray-200">
              {/* Placeholder per immagine */}
              <div className="flex items-center justify-center w-full h-full bg-gray-300">
                <span className="text-gray-600">Immagine E-commerce</span>
              </div>
            </div>
            <div className="order-1 md:order-2">
              <h2 className="mb-6 text-3xl font-bold">E-commerce</h2>
              <p className="mb-4 text-textSecondary">
                Realizziamo piattaforme e-commerce personalizzate e ottimizzate per le conversioni. Dalla piccola boutique online al grande marketplace, costruiamo soluzioni su misura per ogni esigenza.
              </p>
              <ul className="mb-6 space-y-2 text-textSecondary">
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Sviluppo e-commerce personalizzato</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Integrazione con sistemi di pagamento e spedizione</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Gestione catalogo prodotti e magazzino</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Ottimizzazione del processo di checkout</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Analytics e reportistica avanzata</span>
                </li>
              </ul>
              <Link to="/contatti" className="btn btn-primary">
                Richiedi un preventivo
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Digital Marketing */}
      <section id="digital-marketing" className="section">
        <div className="container">
          <div className="grid items-center grid-cols-1 gap-12 md:grid-cols-2">
            <div>
              <h2 className="mb-6 text-3xl font-bold">Digital Marketing</h2>
              <p className="mb-4 text-textSecondary">
                Sviluppiamo strategie di marketing digitale efficaci per aumentare la visibilità online del tuo business, acquisire nuovi clienti e fidelizzare quelli esistenti.
              </p>
              <ul className="mb-6 space-y-2 text-textSecondary">
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>SEO (Search Engine Optimization)</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>SEM e campagne Google Ads</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Social Media Marketing</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Email Marketing</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Content Marketing e creazione contenuti</span>
                </li>
              </ul>
              <Link to="/contatti" className="btn btn-primary">
                Richiedi un preventivo
              </Link>
            </div>
            <div className="w-full h-64 overflow-hidden rounded-lg md:h-96 bg-gray-200">
              {/* Placeholder per immagine */}
              <div className="flex items-center justify-center w-full h-full bg-gray-300">
                <span className="text-gray-600">Immagine Digital Marketing</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Consulenza */}
      <section id="consulting" className="py-16 bg-bgLight">
        <div className="container">
          <div className="grid items-center grid-cols-1 gap-12 md:grid-cols-2">
            <div className="order-2 md:order-1 w-full h-64 overflow-hidden rounded-lg md:h-96 bg-gray-200">
              {/* Placeholder per immagine */}
              <div className="flex items-center justify-center w-full h-full bg-gray-300">
                <span className="text-gray-600">Immagine Consulenza</span>
              </div>
            </div>
            <div className="order-1 md:order-2">
              <h2 className="mb-6 text-3xl font-bold">Consulenza</h2>
              <p className="mb-4 text-textSecondary">
                Offriamo servizi di consulenza strategica per aiutarti a digitalizzare il tuo business, ottimizzare i processi e migliorare la tua presenza online.
              </p>
              <ul className="mb-6 space-y-2 text-textSecondary">
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Analisi e strategia digitale</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Consulenza UX/UI e customer experience</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Ottimizzazione conversioni (CRO)</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Audit tecnico e di performance</span>
                </li>
                <li className="flex items-start">
                  <svg className="w-5 h-5 mr-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd"></path>
                  </svg>
                  <span>Formazione e workshop personalizzati</span>
                </li>
              </ul>
              <Link to="/contatti" className="btn btn-primary">
                Richiedi un preventivo
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 text-white bg-gradient-to-r from-primary to-secondary">
        <div className="container text-center">
          <h2 className="mb-6 text-3xl font-bold md:text-4xl">Hai un progetto in mente?</h2>
          <p className="max-w-2xl mx-auto mb-8 text-lg">
            Contattaci per una consulenza gratuita e scopri come possiamo aiutarti a realizzare il tuo progetto digitale.
          </p>
          <Link to="/contatti" className="btn border-2 border-white hover:bg-white hover:text-primary">
            Contattaci ora
          </Link>
        </div>
      </section>
    </>
  )
}

export default Services 
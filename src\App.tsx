import { useState } from 'react'
import { Routes, Route } from 'react-router-dom'
import Header from './components/Header'
import Footer from './components/Footer'
import SEO from './components/SEO'
import GoogleAnalytics from './components/GoogleAnalytics'
import CookieConsent from './components/CookieConsent'
import Home from './pages/Home'
import About from './pages/About'
import Services from './pages/Services'
import Portfolio from './pages/Portfolio'
import Contact from './pages/Contact'

interface CookiePreferences {
  necessary: boolean
  analytics: boolean
  marketing: boolean
  preferences: boolean
}

function App() {
  const [cookiePreferences, setCookiePreferences] = useState<CookiePreferences>({
    necessary: true,
    analytics: false,
    marketing: false,
    preferences: false,
  })

  const handleConsentChange = (preferences: CookiePreferences) => {
    setCookiePreferences(preferences)
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* SEO Component - will be updated per page */}
      <SEO />

      {/* Google Analytics */}
      <GoogleAnalytics consentGiven={cookiePreferences.analytics} />

      <Header />
      <main className="flex-grow">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/chi-siamo" element={<About />} />
          <Route path="/servizi" element={<Services />} />
          <Route path="/portfolio" element={<Portfolio />} />
          <Route path="/contatti" element={<Contact />} />
        </Routes>
      </main>
      <Footer />

      {/* Cookie Consent Banner */}
      <CookieConsent onConsentChange={handleConsentChange} />
    </div>
  )
}

export default App 
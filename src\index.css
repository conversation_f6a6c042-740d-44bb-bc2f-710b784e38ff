@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    min-height: 100vh;
  }
}

@layer components {
  .container {
    @apply px-4 mx-auto max-w-7xl;
  }
  
  .btn {
    @apply px-6 py-2 text-white transition-colors duration-300 rounded-md;
  }
  
  .btn-primary {
    @apply bg-primary hover:bg-accent;
  }
  
  .btn-secondary {
    @apply bg-secondary hover:bg-primary;
  }
  
  .section {
    @apply py-16;
  }
  
  .section-title {
    @apply mb-12 text-3xl font-bold text-center md:text-4xl;
  }
  
  .card {
    @apply p-6 transition-shadow duration-300 bg-white rounded-lg shadow-md hover:shadow-lg;
  }
  
  .text-shadow {
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.6), 0 0 4px rgba(0, 0, 0, 0.4), 0 0 6px rgba(0, 0, 0, 0.2);
  }

  /* Enhanced modal and slider animations */
  .modal-backdrop {
    backdrop-filter: blur(4px);
    transition: backdrop-filter 300ms ease-in-out;
  }

  .image-slider-container {
    position: relative;
    overflow: hidden;
  }

  .image-slider-transition {
    transition: opacity 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Smooth hover effects for navigation buttons */
  .nav-button {
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }

  .nav-button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  /* Dot indicator animations */
  .dot-indicator {
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  }

  .dot-indicator:hover {
    transform: scale(1.2);
  }
} 
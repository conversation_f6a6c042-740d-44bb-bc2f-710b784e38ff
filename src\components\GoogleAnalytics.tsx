import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'

// Google Analytics configuration
// TODO: Replace with your actual GA4 Measurement ID
const GA_MEASUREMENT_ID = 'G-XXXXXXXXXX' // Replace with your actual GA4 Measurement ID

declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
  }
}

interface GoogleAnalyticsProps {
  consentGiven: boolean
}

const GoogleAnalytics = ({ consentGiven }: GoogleAnalyticsProps) => {
  const location = useLocation()

  useEffect(() => {
    // Initialize Google Analytics only if consent is given
    if (consentGiven && !window.gtag) {
      // Load gtag script
      const script = document.createElement('script')
      script.async = true
      script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`
      document.head.appendChild(script)

      // Initialize dataLayer and gtag
      window.dataLayer = window.dataLayer || []
      window.gtag = function gtag() {
        window.dataLayer.push(arguments)
      }

      // Configure Google Consent Mode v2
      window.gtag('consent', 'default', {
        'analytics_storage': 'denied',
        'ad_storage': 'denied',
        'ad_user_data': 'denied',
        'ad_personalization': 'denied',
        'functionality_storage': 'denied',
        'personalization_storage': 'denied',
        'security_storage': 'granted',
        'wait_for_update': 500,
      })

      // Initialize GA4
      window.gtag('js', new Date())
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_title: document.title,
        page_location: window.location.href,
      })
    }

    // Update consent when consentGiven changes
    if (window.gtag && consentGiven) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'granted',
        'functionality_storage': 'granted',
        'personalization_storage': 'granted',
      })
    }
  }, [consentGiven])

  // Track page views
  useEffect(() => {
    if (window.gtag && consentGiven) {
      window.gtag('config', GA_MEASUREMENT_ID, {
        page_title: document.title,
        page_location: window.location.href,
        page_path: location.pathname,
      })
    }
  }, [location, consentGiven])

  return null
}

// Analytics event tracking functions
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  if (window.gtag) {
    window.gtag('event', eventName, {
      event_category: 'engagement',
      event_label: parameters?.label || '',
      value: parameters?.value || 0,
      ...parameters,
    })
  }
}

export const trackCTAClick = (ctaName: string, location: string) => {
  trackEvent('cta_click', {
    event_category: 'cta',
    cta_name: ctaName,
    cta_location: location,
  })
}

export const trackNavigation = (destination: string, source: string) => {
  trackEvent('navigation_click', {
    event_category: 'navigation',
    destination,
    source,
  })
}

export const trackModalOpen = (modalType: string, projectId?: string) => {
  trackEvent('modal_open', {
    event_category: 'modal',
    modal_type: modalType,
    project_id: projectId,
  })
}

export const trackModalClose = (modalType: string, projectId?: string) => {
  trackEvent('modal_close', {
    event_category: 'modal',
    modal_type: modalType,
    project_id: projectId,
  })
}

export const trackImageSlider = (action: string, imageIndex: number, projectId?: string) => {
  trackEvent('image_slider_interaction', {
    event_category: 'slider',
    slider_action: action,
    image_index: imageIndex,
    project_id: projectId,
  })
}

export const trackPortfolioFilter = (filterCategory: string) => {
  trackEvent('portfolio_filter', {
    event_category: 'portfolio',
    filter_category: filterCategory,
  })
}

export const trackFormSubmission = (formType: string, success: boolean) => {
  trackEvent('form_submission', {
    event_category: 'form',
    form_type: formType,
    success: success ? 'true' : 'false',
  })
}

export default GoogleAnalytics

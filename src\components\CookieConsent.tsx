import { useState, useEffect } from 'react'

interface CookiePreferences {
  necessary: boolean
  analytics: boolean
  marketing: boolean
  preferences: boolean
}

interface CookieConsentProps {
  onConsentChange: (consent: CookiePreferences) => void
}

const CookieConsent = ({ onConsentChange }: CookieConsentProps) => {
  const [showBanner, setShowBanner] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [preferences, setPreferences] = useState<CookiePreferences>({
    necessary: true, // Always true, cannot be disabled
    analytics: false,
    marketing: false,
    preferences: false,
  })

  useEffect(() => {
    // Check if user has already made a choice
    const savedConsent = localStorage.getItem('cookie-consent')
    if (savedConsent) {
      const parsedConsent = JSON.parse(savedConsent)
      setPreferences(parsedConsent)
      onConsentChange(parsedConsent)
    } else {
      // Show banner if no previous consent
      setShowBanner(true)
    }
  }, [onConsentChange])

  const saveConsent = (newPreferences: CookiePreferences) => {
    localStorage.setItem('cookie-consent', JSON.stringify(newPreferences))
    localStorage.setItem('cookie-consent-date', new Date().toISOString())
    setPreferences(newPreferences)
    onConsentChange(newPreferences)
    setShowBanner(false)
    setShowDetails(false)
  }

  const acceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
    }
    saveConsent(allAccepted)
  }

  const acceptNecessaryOnly = () => {
    const necessaryOnly = {
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false,
    }
    saveConsent(necessaryOnly)
  }

  const saveCustomPreferences = () => {
    saveConsent(preferences)
  }

  const handlePreferenceChange = (type: keyof CookiePreferences) => {
    if (type === 'necessary') return // Cannot disable necessary cookies
    
    setPreferences(prev => ({
      ...prev,
      [type]: !prev[type]
    }))
  }

  if (!showBanner) return null

  return (
    <div className="fixed inset-0 z-50 flex items-end justify-center p-4 pointer-events-none">
      <div className="w-full max-w-4xl bg-white rounded-lg shadow-2xl border border-border pointer-events-auto">
        {!showDetails ? (
          // Simple banner
          <div className="p-6">
            <div className="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-textPrimary mb-2">
                  Utilizziamo i cookie per migliorare la tua esperienza
                </h3>
                <p className="text-textSecondary text-sm">
                  Utilizziamo cookie tecnici e, previo tuo consenso, cookie di profilazione per offrirti 
                  un'esperienza di navigazione personalizzata. Puoi accettare tutti i cookie o gestire le tue preferenze.
                </p>
              </div>
              <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
                <button
                  onClick={() => setShowDetails(true)}
                  className="px-4 py-2 text-sm font-medium text-primary border border-primary rounded-md hover:bg-primary hover:text-white transition-colors"
                >
                  Gestisci preferenze
                </button>
                <button
                  onClick={acceptNecessaryOnly}
                  className="px-4 py-2 text-sm font-medium text-textSecondary border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Solo necessari
                </button>
                <button
                  onClick={acceptAll}
                  className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-accent transition-colors"
                >
                  Accetta tutti
                </button>
              </div>
            </div>
          </div>
        ) : (
          // Detailed preferences
          <div className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-textPrimary mb-2">
                Gestisci le tue preferenze sui cookie
              </h3>
              <p className="text-textSecondary text-sm">
                Puoi scegliere quali categorie di cookie accettare. I cookie necessari sono sempre attivi 
                per garantire il funzionamento del sito.
              </p>
            </div>

            <div className="space-y-4 mb-6">
              {/* Necessary Cookies */}
              <div className="flex items-start space-x-3">
                <div className="flex items-center h-5">
                  <input
                    type="checkbox"
                    checked={true}
                    disabled={true}
                    className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary"
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-textPrimary">Cookie Necessari</h4>
                  <p className="text-sm text-textSecondary">
                    Essenziali per il funzionamento del sito web. Non possono essere disabilitati.
                  </p>
                </div>
              </div>

              {/* Analytics Cookies */}
              <div className="flex items-start space-x-3">
                <div className="flex items-center h-5">
                  <input
                    type="checkbox"
                    checked={preferences.analytics}
                    onChange={() => handlePreferenceChange('analytics')}
                    className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary"
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-textPrimary">Cookie Analitici</h4>
                  <p className="text-sm text-textSecondary">
                    Ci aiutano a capire come i visitatori interagiscono con il sito raccogliendo informazioni anonime.
                  </p>
                </div>
              </div>

              {/* Marketing Cookies */}
              <div className="flex items-start space-x-3">
                <div className="flex items-center h-5">
                  <input
                    type="checkbox"
                    checked={preferences.marketing}
                    onChange={() => handlePreferenceChange('marketing')}
                    className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary"
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-textPrimary">Cookie di Marketing</h4>
                  <p className="text-sm text-textSecondary">
                    Utilizzati per tracciare i visitatori sui siti web per mostrare annunci pertinenti e coinvolgenti.
                  </p>
                </div>
              </div>

              {/* Preference Cookies */}
              <div className="flex items-start space-x-3">
                <div className="flex items-center h-5">
                  <input
                    type="checkbox"
                    checked={preferences.preferences}
                    onChange={() => handlePreferenceChange('preferences')}
                    className="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary"
                  />
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-textPrimary">Cookie di Preferenze</h4>
                  <p className="text-sm text-textSecondary">
                    Permettono al sito di ricordare le scelte fatte per fornire funzionalità migliorate e personalizzate.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2">
              <button
                onClick={() => setShowDetails(false)}
                className="px-4 py-2 text-sm font-medium text-textSecondary border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Indietro
              </button>
              <button
                onClick={acceptNecessaryOnly}
                className="px-4 py-2 text-sm font-medium text-textSecondary border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Solo necessari
              </button>
              <button
                onClick={saveCustomPreferences}
                className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-accent transition-colors"
              >
                Salva preferenze
              </button>
              <button
                onClick={acceptAll}
                className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-accent transition-colors"
              >
                Accetta tutti
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default CookieConsent

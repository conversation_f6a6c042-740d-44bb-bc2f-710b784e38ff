import { useState } from 'react'
import { Link } from 'react-router-dom'
import WaveAnimation from '../components/WaveAnimation'
import Modal from '../components/Modal'
import ImageSlider from '../components/ImageSlider'

// Definizione dell'interfaccia per i progetti
interface Project {
  id: number
  title: string
  category: string
  description: string
  fullDescription: string
  images: string[]
  tags: string[]
}

const Home = () => {
  // Stati per la modal
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedProject, setSelectedProject] = useState<Project | null>(null)

  // Funzione per aprire la modal
  const openModal = (project: Project) => {
    setSelectedProject(project)
    setIsModalOpen(true)
  }

  // Funzione per chiudere la modal
  const closeModal = () => {
    setIsModalOpen(false)
    setSelectedProject(null)
  }

  // Progetti in evidenza (stesso formato di quelli in Portfolio)
  const featuredProjects: Project[] = [
    {
      id: 1,
      title: 'Centro Telefonia Noli',
      category: 'E-commerce',
      description: 'Sviluppo di un e-commerce completo per vendita Smartphone e accessori, con integrazione pagamenti e gestione magazzino.',
      fullDescription: 'Abbiamo sviluppato una piattaforma e-commerce completa per un affermato negozio di Smartphone e accessori. Il progetto ha incluso la progettazione dell\'interfaccia utente, lo sviluppo front-end e back-end, l\'integrazione con gateway di pagamento Stripe, e la configurazione di un sistema di gestione del magazzino. La soluzione ha permesso al brand di aumentare le vendite online del 150% nel primo trimestre dopo il lancio.',
      images: [
        'src/assets/images/centro_noli/noli_1.png',
        'src/assets/images/centro_noli/noli_2.png',
        'src/assets/images/centro_noli/noli_3.png'
      ],
      tags: ['E-commerce', 'Woocommerce', 'Paypal', 'Stripe'],
    },
    {
      id: 2,
      title: 'Talenti del Padel',
      category: 'E-commerce',
      description: 'Sviluppo di un e-commerce completo per vendita Racchette da padel ed attrezzatura sportiva da padel, con integrazione pagamenti e gestione magazzino.',
      fullDescription: 'Abbiamo sviluppato una piattaforma e-commerce completa per un affermato negozio di Racchette da padel ed attrezzatura sportiva da padel. Il progetto ha incluso la progettazione dell\'interfaccia utente, lo sviluppo front-end e back-end, l\'integrazione con gateway di pagamento Stripe, e la configurazione di un sistema di gestione del magazzino. La soluzione ha permesso al brand di aumentare le vendite online del 150% nel primo trimestre dopo il lancio.',
      images: [
        'src/assets/images/talenti/talenti_1.png',
        'src/assets/images/talenti/talenti_2.png',
        'src/assets/images/talenti/talenti_3.png'
      ],
      tags: ['E-commerce', 'Woocommerce', 'Paypal', 'Stripe'],
    },
    {
      id: 3,
      title: 'App Delivery Food',
      category: 'Web Development',
      description: 'Sviluppo app mobile per servizio di consegna cibo, con gestione ordini e tracking in tempo reale.',
      fullDescription: 'Abbiamo creato un\'applicazione mobile cross-platform per un servizio di food delivery locale. L\'app permette agli utenti di sfogliare menu, effettuare ordini, pagare e tracciare le consegne in tempo reale. Sul lato ristoratore, abbiamo implementato un sistema di gestione degli ordini che ha ottimizzato i tempi di preparazione e consegna del 30%.',
      images: ['food1.jpg', 'food2.jpg', 'food3.jpg'],
      tags: ['React Native', 'Firebase', 'UX Design'],
    },
  ]

  return (
    <>
      {/* Hero Section */}
      <section className="relative py-32 pb-48 bg-gradient-to-r from-secondary to-primary text-white">
        <div className="container">
          <div className="max-w-3xl">
            <h1 className="mb-6 text-4xl font-bold md:text-5xl lg:text-6xl text-shadow">
              Soluzioni digitali su misura per il tuo business
            </h1>
            <p className="mb-8 text-lg md:text-xl text-shadow">
              Web development e servizi di marketing e-commerce personalizzati per far crescere la tua presenza online.
            </p>
            <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
              <Link to="/contatti" className="btn btn-primary">
                Contattaci
              </Link>
              <Link to="/servizi" className="btn border-2 border-white hover:bg-white hover:text-primary">
                Scopri i servizi
              </Link>
            </div>
          </div>
        </div>
        
        {/* Wave Shape */}
        <WaveAnimation />
      </section>

      {/* Servizi in evidenza */}
      <section className="section">
        <div className="container">
          <h2 className="section-title">I Nostri Servizi</h2>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            
            {/* Servizio 1 */}
            <div className="card">
              <div className="flex items-center justify-center w-16 h-16 mb-4 text-white rounded-lg bg-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="mb-3 text-xl font-bold">Web Development</h3>
              <p className="mb-4 text-textSecondary">
                Sviluppiamo siti web moderni, responsivi e performanti utilizzando le tecnologie più aggiornate.
              </p>
              <Link to="/servizi#web-development" className="inline-block font-medium text-primary hover:text-accent">
                Scopri di più &rarr;
              </Link>
            </div>

            {/* Servizio 2 */}
            <div className="card">
              <div className="flex items-center justify-center w-16 h-16 mb-4 text-white rounded-lg bg-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <h3 className="mb-3 text-xl font-bold">E-commerce</h3>
              <p className="mb-4 text-textSecondary">
                Creiamo piattaforme e-commerce su misura, ottimizzate per le conversioni e facili da gestire.
              </p>
              <Link to="/servizi#ecommerce" className="inline-block font-medium text-primary hover:text-accent">
                Scopri di più &rarr;
              </Link>
            </div>

            {/* Servizio 3 */}
            <div className="card">
              <div className="flex items-center justify-center w-16 h-16 mb-4 text-white rounded-lg bg-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                </svg>
              </div>
              <h3 className="mb-3 text-xl font-bold">Digital Marketing</h3>
              <p className="mb-4 text-textSecondary">
                Strategie di marketing digitale per aumentare la visibilità del tuo business e acquisire nuovi clienti.
              </p>
              <Link to="/servizi#digital-marketing" className="inline-block font-medium text-primary hover:text-accent">
                Scopri di più &rarr;
              </Link>
            </div>

            {/* Servizio 4 */}
            <div className="card">
              <div className="flex items-center justify-center w-16 h-16 mb-4 text-white rounded-lg bg-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="mb-3 text-xl font-bold">Consulenza</h3>
              <p className="mb-4 text-textSecondary">
                Supporto e consulenza strategica per digitalizzare il tuo business e ottimizzare i processi online.
              </p>
              <Link to="/servizi#consulting" className="inline-block font-medium text-primary hover:text-accent">
                Scopri di più &rarr;
              </Link>
            </div>
          </div>
          <div className="mt-12 text-center">
            <Link to="/servizi" className="btn btn-primary">
              Tutti i servizi
            </Link>
          </div>
        </div>
      </section>

      {/* Chi Siamo in breve */}
      <section className="py-16 bg-bgDark text-white">
        <div className="container">
          <div className="grid items-center grid-cols-1 gap-12 md:grid-cols-2">
            <div>
              <h2 className="mb-6 text-3xl font-bold md:text-4xl">Chi Siamo</h2>
              <p className="mb-6">
                Programmarti è un'agenzia specializzata in sviluppo web e marketing digitale con sede a Roma. 
                Il nostro team di esperti sviluppatori e marketer è dedicato a creare soluzioni digitali 
                che aiutano le aziende a crescere online.
              </p>
              <p className="mb-8">
                Con anni di esperienza nel settore, offriamo servizi personalizzati che si adattano 
                alle specifiche esigenze di ogni cliente, garantendo risultati concreti e misurabili.
              </p>
              <Link to="/chi-siamo" className="btn border-2 border-white hover:bg-white hover:text-primary">
                Scopri di più
              </Link>
            </div>
            <div className="w-full h-64 overflow-hidden rounded-lg md:h-96 bg-gray-600">
              {/* Placeholder per immagine */}
              <div className="flex items-center justify-center w-full h-full bg-gray-700">
                <span className="text-lg text-gray-300">Immagine Team</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio in evidenza */}
      <section className="section">
        <div className="container">
          <h2 className="section-title">Progetti Recenti</h2>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {/* Progetto 1 */}
            <div 
              className="overflow-hidden transition-transform duration-300 rounded-lg shadow-md hover:-translate-y-2 cursor-pointer"
              onClick={() => openModal(featuredProjects[0])}
            >
              <div className="w-full h-48 overflow-hidden bg-gray-200">
                <img 
                  src={featuredProjects[0].images[0]} 
                  alt={featuredProjects[0].title} 
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="p-6">
                <h3 className="mb-2 text-xl font-bold">{featuredProjects[0].title}</h3>
                <p className="mb-4 text-textSecondary">
                  {featuredProjects[0].description}
                </p>
                <Link to="/portfolio" className="inline-block font-medium text-primary hover:text-accent">
                  Vedi dettagli &rarr;
                </Link>
              </div>
            </div>

            {/* Progetto 2 */}
            <div 
              className="overflow-hidden transition-transform duration-300 rounded-lg shadow-md hover:-translate-y-2 cursor-pointer"
              onClick={() => openModal(featuredProjects[1])}
            >
              <div className="w-full h-48 overflow-hidden bg-gray-200">
                <img 
                  src={featuredProjects[1].images[0]} 
                  alt={featuredProjects[1].title} 
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="p-6">
                <h3 className="mb-2 text-xl font-bold">{featuredProjects[1].title}</h3>
                <p className="mb-4 text-textSecondary">
                  {featuredProjects[1].description}
                </p>
                <Link to="/portfolio" className="inline-block font-medium text-primary hover:text-accent">
                  Vedi dettagli &rarr;
                </Link>
              </div>
            </div>

            {/* Progetto 3 */}
            <div 
              className="overflow-hidden transition-transform duration-300 rounded-lg shadow-md hover:-translate-y-2 cursor-pointer"
              onClick={() => openModal(featuredProjects[2])}
            >
              <div className="w-full h-48 overflow-hidden bg-gray-200">
                <img 
                  src={featuredProjects[2].images[0]} 
                  alt={featuredProjects[2].title} 
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="p-6">
                <h3 className="mb-2 text-xl font-bold">{featuredProjects[2].title}</h3>
                <p className="mb-4 text-textSecondary">
                  {featuredProjects[2].description}
                </p>
                <Link to="/portfolio" className="inline-block font-medium text-primary hover:text-accent">
                  Vedi dettagli &rarr;
                </Link>
              </div>
            </div>
          </div>
          <div className="mt-12 text-center">
            <Link to="/portfolio" className="btn btn-primary">
              Vedi tutti i progetti
            </Link>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 text-white bg-gradient-to-r from-primary to-secondary">
        <div className="container text-center">
          <h2 className="mb-6 text-3xl font-bold md:text-4xl">Pronto a trasformare la tua presenza online?</h2>
          <p className="max-w-2xl mx-auto mb-8 text-lg">
            Contattaci oggi stesso per una consulenza gratuita e scopri come possiamo aiutarti a far crescere il tuo business.
          </p>
          <Link to="/contatti" className="btn border-2 border-white hover:bg-white hover:text-primary">
            Contattaci ora
          </Link>
        </div>
      </section>

      {/* Modal per i dettagli del progetto */}
      <Modal isOpen={isModalOpen} onClose={closeModal}>
        {selectedProject && (
          <div className="flex flex-col md:flex-row">
            {/* Colonna sinistra con slider immagini */}
            <div className="w-full md:w-1/2 h-[300px] md:h-auto">
              <ImageSlider
                images={selectedProject.images}
                projectTitle={selectedProject.title}
                className="w-full h-full"
              />
            </div>
            
            {/* Colonna destra con dettagli */}
            <div className="w-full md:w-1/2 p-8 overflow-y-auto">
              <h2 className="mb-4 text-2xl font-bold text-primary">{selectedProject.title}</h2>
              
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-700">Categoria</h3>
                <p className="text-textSecondary">{selectedProject.category}</p>
              </div>
              
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-700">Tag</h3>
                <div className="flex flex-wrap gap-2 mt-2">
                  {selectedProject.tags.map(tag => (
                    <span key={tag} className="px-3 py-1 text-sm text-white rounded-full bg-primary">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-700">Descrizione</h3>
                <p className="mt-2 text-textSecondary">
                  {selectedProject.fullDescription}
                </p>
              </div>
              
              <button 
                className="px-6 py-3 mt-8 text-white transition-colors rounded bg-primary hover:bg-accent"
              >
                Richiedi un progetto simile
              </button>
            </div>
          </div>
        )}
      </Modal>
    </>
  )
}

export default Home 
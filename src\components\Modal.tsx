import { useEffect, useState } from 'react'

interface ModalProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  maxWidth?: string
}

const Modal = ({ isOpen, onClose, children, maxWidth = 'max-w-6xl' }: ModalProps) => {
  const [isVisible, setIsVisible] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true)
      // Small delay to trigger the fade-in animation
      setTimeout(() => setIsAnimating(true), 10)
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'
    } else {
      setIsAnimating(false)
      // Wait for animation to complete before hiding
      setTimeout(() => setIsVisible(false), 300)
      // Restore body scroll
      document.body.style.overflow = 'auto'
    }

    // Cleanup function to restore scroll on unmount
    return () => {
      document.body.style.overflow = 'auto'
    }
  }, [isOpen])

  // Handle escape key press
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  if (!isVisible) return null

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 modal-backdrop transition-all duration-300 ease-in-out ${
        isAnimating
          ? 'bg-black bg-opacity-70'
          : 'bg-black bg-opacity-0'
      }`}
      onClick={handleBackdropClick}
    >
      <div 
        className={`relative w-full ${maxWidth} max-h-[90vh] overflow-hidden bg-white rounded-lg shadow-xl transition-all duration-300 ease-in-out transform ${
          isAnimating 
            ? 'opacity-100 scale-100 translate-y-0' 
            : 'opacity-0 scale-95 translate-y-4'
        }`}
      >
        {/* Close button */}
        <button 
          onClick={onClose}
          className="absolute z-10 p-2 text-white transition-all duration-200 bg-primary hover:bg-accent rounded-full top-4 right-4 hover:scale-110"
          aria-label="Close modal"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        {children}
      </div>
    </div>
  )
}

export default Modal

import { Link } from 'react-router-dom'
import WaveAnimation from '../components/WaveAnimation'
import SEO from '../components/SEO'
import { trackCTAClick } from '../components/GoogleAnalytics'

const About = () => {
  return (
    <>
      {/* SEO for About Page */}
      <SEO
        title="Chi Siamo | Programmarti - Agenzia Web Development Roma"
        description="Scopri la storia, i valori e il team di Programmarti. Agenzia di sviluppo web e marketing digitale a Roma specializzata in soluzioni digitali personalizzate per il tuo business."
        keywords="chi siamo, agenzia web Roma, team sviluppo web, storia programmarti, valori aziendali, sviluppatori Roma"
        type="website"
      />

      {/* Hero Section */}
      <section className="relative py-20 pb-48 text-white bg-gradient-to-r from-secondary to-primary">
        <div className="container">
          <div className="text-center">
            <h1 className="mb-6 text-4xl font-bold md:text-5xl text-shadow">
              Chi Siamo
            </h1>
            <p className="max-w-2xl mx-auto text-lg md:text-xl text-shadow">
              Agenzia di sviluppo web e marketing digitale a Roma, specializzata in soluzioni digitali personalizzate.
            </p>
          </div>
        </div>
        
        {/* Wave Shape */}
        <WaveAnimation />
      </section>

      {/* La Nostra Storia */}
      <section className="section">
        <div className="container">
          <div className="grid items-center grid-cols-1 gap-12 md:grid-cols-2">
            <div className="w-full h-64 overflow-hidden rounded-lg md:h-96 bg-gray-200">
              {/* Placeholder per immagine */}
              <div className="flex items-center justify-center w-full h-full bg-gray-300">
                <span className="text-gray-600">Immagine Ufficio</span>
              </div>
            </div>
            <div>
              <h2 className="mb-6 text-3xl font-bold">La Nostra Storia</h2>
              <p className="mb-4 text-textSecondary">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum pellentesque nisi id nunc ultrices, in tincidunt nisi malesuada. Phasellus ut euismod velit, quis lacinia felis. Proin maximus, odio vel ornare varius, sem velit hendrerit ipsum, in commodo felis eros quis felis.
              </p>
              <p className="mb-4 text-textSecondary">
                Cras sit amet orci lacinia, scelerisque neque ac, volutpat arcu. Donec tincidunt purus non erat dictum, ut pellentesque ipsum interdum. In hac habitasse platea dictumst. Vestibulum eu neque congue, egestas est ut, pretium elit.
              </p>
              <p className="text-textSecondary">
                Fusce posuere, nulla eu finibus imperdiet, tortor tellus commodo dui, non vulputate orci nisi id orci. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* I Nostri Valori */}
      <section className="py-16 bg-bgLight">
        <div className="container">
          <h2 className="section-title">I Nostri Valori</h2>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {/* Valore 1 */}
            <div className="card">
              <div className="flex items-center justify-center w-16 h-16 mb-4 text-white rounded-full bg-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="mb-3 text-xl font-bold">Innovazione</h3>
              <p className="text-textSecondary">
                Utilizziamo le tecnologie più recenti e adottiamo un approccio creativo per risolvere le sfide dei nostri clienti.
              </p>
            </div>

            {/* Valore 2 */}
            <div className="card">
              <div className="flex items-center justify-center w-16 h-16 mb-4 text-white rounded-full bg-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="mb-3 text-xl font-bold">Collaborazione</h3>
              <p className="text-textSecondary">
                Crediamo nel potere della collaborazione e nel costruire relazioni di lungo termine con i nostri clienti.
              </p>
            </div>

            {/* Valore 3 */}
            <div className="card">
              <div className="flex items-center justify-center w-16 h-16 mb-4 text-white rounded-full bg-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <h3 className="mb-3 text-xl font-bold">Qualità</h3>
              <p className="text-textSecondary">
                Ci impegniamo a fornire soluzioni di alta qualità, con attenzione ai dettagli e all'esperienza dell'utente.
              </p>
            </div>

            {/* Valore 4 */}
            <div className="card">
              <div className="flex items-center justify-center w-16 h-16 mb-4 text-white rounded-full bg-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="mb-3 text-xl font-bold">Puntualità</h3>
              <p className="text-textSecondary">
                Rispettiamo le scadenze e ci impegniamo a consegnare i progetti nei tempi concordati con il cliente.
              </p>
            </div>

            {/* Valore 5 */}
            <div className="card">
              <div className="flex items-center justify-center w-16 h-16 mb-4 text-white rounded-full bg-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <h3 className="mb-3 text-xl font-bold">Creatività</h3>
              <p className="text-textSecondary">
                Ogni progetto è unico e richiede soluzioni originali. Portiamo creatività e idee innovative in ogni lavoro.
              </p>
            </div>

            {/* Valore 6 */}
            <div className="card">
              <div className="flex items-center justify-center w-16 h-16 mb-4 text-white rounded-full bg-primary">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
              <h3 className="mb-3 text-xl font-bold">Trasparenza</h3>
              <p className="text-textSecondary">
                Comunichiamo in modo chiaro e aperto con i nostri clienti, mantenendo la massima trasparenza in ogni fase del progetto.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="section">
        <div className="container">
          <h2 className="section-title">Il Nostro Team</h2>
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            {/* Membro 1 */}
            <div className="text-center">
              <div className="relative w-40 h-40 mx-auto mb-4 overflow-hidden rounded-full">
                {/* Placeholder per immagine */}
                <div className="flex items-center justify-center w-full h-full bg-gray-300">
                  <span className="text-gray-600">Foto</span>
                </div>
              </div>
              <h3 className="mb-1 text-xl font-bold">Marco Rossi</h3>
              <p className="mb-3 text-primary">CEO & Fondatore</p>
              <p className="text-textSecondary">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla facilisis erat a libero efficitur.
              </p>
            </div>

            {/* Membro 2 */}
            <div className="text-center">
              <div className="relative w-40 h-40 mx-auto mb-4 overflow-hidden rounded-full">
                {/* Placeholder per immagine */}
                <div className="flex items-center justify-center w-full h-full bg-gray-300">
                  <span className="text-gray-600">Foto</span>
                </div>
              </div>
              <h3 className="mb-1 text-xl font-bold">Laura Bianchi</h3>
              <p className="mb-3 text-primary">Lead Designer</p>
              <p className="text-textSecondary">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla facilisis erat a libero efficitur.
              </p>
            </div>

            {/* Membro 3 */}
            <div className="text-center">
              <div className="relative w-40 h-40 mx-auto mb-4 overflow-hidden rounded-full">
                {/* Placeholder per immagine */}
                <div className="flex items-center justify-center w-full h-full bg-gray-300">
                  <span className="text-gray-600">Foto</span>
                </div>
              </div>
              <h3 className="mb-1 text-xl font-bold">Antonio Verdi</h3>
              <p className="mb-3 text-primary">Senior Developer</p>
              <p className="text-textSecondary">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla facilisis erat a libero efficitur.
              </p>
            </div>

            {/* Membro 4 */}
            <div className="text-center">
              <div className="relative w-40 h-40 mx-auto mb-4 overflow-hidden rounded-full">
                {/* Placeholder per immagine */}
                <div className="flex items-center justify-center w-full h-full bg-gray-300">
                  <span className="text-gray-600">Foto</span>
                </div>
              </div>
              <h3 className="mb-1 text-xl font-bold">Sofia Neri</h3>
              <p className="mb-3 text-primary">Digital Marketing Specialist</p>
              <p className="text-textSecondary">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla facilisis erat a libero efficitur.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 text-white bg-gradient-to-r from-primary to-secondary">
        <div className="container text-center">
          <h2 className="mb-6 text-3xl font-bold md:text-4xl">Vuoi lavorare con noi?</h2>
          <p className="max-w-2xl mx-auto mb-8 text-lg">
            Siamo sempre alla ricerca di talenti per il nostro team. Inviaci il tuo CV e parliamo delle opportunità disponibili.
          </p>
          <Link
            to="/contatti"
            className="btn border-2 border-white hover:bg-white hover:text-primary"
            onClick={() => trackCTAClick('Contattaci', 'about_cta_section')}
          >
            Contattaci
          </Link>
        </div>
      </section>
    </>
  )
}

export default About 
import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'

interface SEOProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  url?: string
  type?: string
}

const SEO = ({ 
  title, 
  description, 
  keywords, 
  image = '/favicon-512x512.png',
  url,
  type = 'website' 
}: SEOProps) => {
  const location = useLocation()
  const baseUrl = 'https://programmarti.com' // Replace with your actual domain
  const currentUrl = url || `${baseUrl}${location.pathname}`

  // Default SEO values
  const defaultTitle = 'Programmarti | Web Development & E-commerce Marketing | Roma'
  const defaultDescription = 'Agenzia di sviluppo web e servizi di marketing e-commerce a Roma. Soluzioni digitali personalizzate per il tuo business. Sviluppo siti web, e-commerce, UI/UX design e digital marketing.'
  const defaultKeywords = 'web development, sviluppo web, e-commerce, digital marketing, UI/UX design, agenzia web Roma, siti web, marketing digitale, programmarti'

  const seoTitle = title || defaultTitle
  const seoDescription = description || defaultDescription
  const seoKeywords = keywords || defaultKeywords
  const seoImage = image.startsWith('http') ? image : `${baseUrl}${image}`

  useEffect(() => {
    // Update document title
    document.title = seoTitle

    // Update or create meta tags
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`
      let meta = document.querySelector(selector) as HTMLMetaElement
      
      if (!meta) {
        meta = document.createElement('meta')
        if (property) {
          meta.setAttribute('property', name)
        } else {
          meta.setAttribute('name', name)
        }
        document.head.appendChild(meta)
      }
      meta.setAttribute('content', content)
    }

    // Basic meta tags
    updateMetaTag('description', seoDescription)
    updateMetaTag('keywords', seoKeywords)
    updateMetaTag('author', 'Programmarti')
    updateMetaTag('robots', 'index, follow')

    // Open Graph tags
    updateMetaTag('og:title', seoTitle, true)
    updateMetaTag('og:description', seoDescription, true)
    updateMetaTag('og:type', type, true)
    updateMetaTag('og:url', currentUrl, true)
    updateMetaTag('og:image', seoImage, true)
    updateMetaTag('og:image:alt', seoTitle, true)
    updateMetaTag('og:site_name', 'Programmarti', true)
    updateMetaTag('og:locale', 'it_IT', true)

    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image')
    updateMetaTag('twitter:title', seoTitle)
    updateMetaTag('twitter:description', seoDescription)
    updateMetaTag('twitter:image', seoImage)
    updateMetaTag('twitter:image:alt', seoTitle)

    // Additional meta tags
    updateMetaTag('theme-color', '#007ACC')
    updateMetaTag('msapplication-TileColor', '#007ACC')

    // Canonical URL
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement
    if (!canonical) {
      canonical = document.createElement('link')
      canonical.setAttribute('rel', 'canonical')
      document.head.appendChild(canonical)
    }
    canonical.setAttribute('href', currentUrl)

    // Structured data for organization
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Programmarti",
      "description": seoDescription,
      "url": baseUrl,
      "logo": `${baseUrl}/favicon-512x512.png`,
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Roma",
        "addressCountry": "IT"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "availableLanguage": "Italian"
      },
      "sameAs": [
        // Add your social media URLs here
      ]
    }

    // Update or create structured data script
    let structuredDataScript = document.querySelector('script[type="application/ld+json"]')
    if (!structuredDataScript) {
      structuredDataScript = document.createElement('script')
      structuredDataScript.setAttribute('type', 'application/ld+json')
      document.head.appendChild(structuredDataScript)
    }
    structuredDataScript.textContent = JSON.stringify(structuredData)

  }, [seoTitle, seoDescription, seoKeywords, seoImage, currentUrl, type])

  return null // This component doesn't render anything
}

export default SEO

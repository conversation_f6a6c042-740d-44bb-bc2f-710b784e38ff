import { useState, useEffect } from 'react'
import { trackImageSlider } from './GoogleAnalytics'

interface ImageSliderProps {
  images: string[]
  projectTitle: string
  className?: string
  projectId?: string
}

const ImageSlider = ({ images, projectTitle, className = '', projectId }: ImageSliderProps) => {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTransitioning, setIsTransitioning] = useState(false)

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        goToPrevious()
      } else if (e.key === 'ArrowRight') {
        goToNext()
      }
    }

    document.addEventListener('keydown', handleKeyPress)
    return () => document.removeEventListener('keydown', handleKeyPress)
  }, [])

  const goToNext = () => {
    if (isTransitioning) return

    setIsTransitioning(true)
    setTimeout(() => {
      const newIndex = currentIndex === images.length - 1 ? 0 : currentIndex + 1
      setCurrentIndex(newIndex)
      trackImageSlider('next', newIndex, projectId)
      setIsTransitioning(false)
    }, 150)
  }

  const goToPrevious = () => {
    if (isTransitioning) return

    setIsTransitioning(true)
    setTimeout(() => {
      const newIndex = currentIndex === 0 ? images.length - 1 : currentIndex - 1
      setCurrentIndex(newIndex)
      trackImageSlider('previous', newIndex, projectId)
      setIsTransitioning(false)
    }, 150)
  }

  const goToSlide = (index: number) => {
    if (isTransitioning || index === currentIndex) return

    setIsTransitioning(true)
    setTimeout(() => {
      setCurrentIndex(index)
      trackImageSlider('dot_click', index, projectId)
      setIsTransitioning(false)
    }, 150)
  }

  if (!images || images.length === 0) {
    return (
      <div className={`flex items-center justify-center bg-gray-200 ${className}`}>
        <span className="text-gray-500">No images available</span>
      </div>
    )
  }

  return (
    <div className={`relative bg-gray-100 image-slider-container ${className}`}>
      {/* Main image container */}
      <div className="relative w-full h-full overflow-hidden">
        <div
          className={`w-full h-full image-slider-transition ${
            isTransitioning ? 'opacity-0' : 'opacity-100'
          }`}
        >
          <img 
            src={images[currentIndex]} 
            alt={`${projectTitle} - Image ${currentIndex + 1}`}
            className="object-contain w-full h-full" 
          />
        </div>
        
        {/* Navigation arrows - only show if more than one image */}
        {images.length > 1 && (
          <>
            <button
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 p-2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full nav-button"
              aria-label="Previous image"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            
            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 p-2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full nav-button"
              aria-label="Next image"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </>
        )}
        
        {/* Image counter */}
        {images.length > 1 && (
          <div className="absolute top-4 left-4 px-3 py-1 bg-black bg-opacity-50 text-white text-sm rounded-full">
            {currentIndex + 1} / {images.length}
          </div>
        )}
      </div>
      
      {/* Dots indicator - only show if more than one image */}
      {images.length > 1 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full dot-indicator ${
                index === currentIndex
                  ? 'bg-white scale-110'
                  : 'bg-white bg-opacity-50 hover:bg-opacity-75'
              }`}
              aria-label={`Go to image ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default ImageSlider
